{"name": "clw-cli", "version": "1.1.11", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "npm link -f"}, "keywords": [], "author": "", "license": "ISC", "bin": {"clwcli": "index.js"}, "dependencies": {"chalk": "^4.1.0", "commander": "^6.2.0", "download-git-repo": "^3.0.2", "handlebars": "^4.7.6", "inquirer": "^7.3.3", "log-symbols": "^4.0.0", "ora": "^5.1.0", "prompt": "^1.2.0", "shelljs": "^0.8.4"}}